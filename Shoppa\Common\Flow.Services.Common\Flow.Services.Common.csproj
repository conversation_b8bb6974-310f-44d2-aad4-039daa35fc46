﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9790E802-7EF3-46BE-BC4D-681DF9D5E9F8}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Flow.Services.Common</RootNamespace>
    <AssemblyName>Flow.Services.Common</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\ShoppaAB\Public\ShoppaAB.Flow\ShoppaAB.Flow.csproj">
      <Project>{9d232a86-9b79-4bdd-b462-4f1115c6c35b}</Project>
      <Name>ShoppaAB.Flow</Name>
    </ProjectReference>
    <ProjectReference Include="..\Flow.Services.DataLayer.MySQL\Flow.Services.DataLayer.MySQL.csproj">
      <Project>{e74a3ec2-0995-4a94-9345-c0104b000bc2}</Project>
      <Name>Flow.Services.DataLayer.MySQL</Name>
    </ProjectReference>
    <ProjectReference Include="..\SunFlow.Objects\SunFlow.Objects.csproj">
      <Project>{e49a2ea9-e041-4a13-a5f0-31194f5a1a0d}</Project>
      <Name>SunFlow.Objects</Name>
    </ProjectReference>
  </ItemGroup>

  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
</Project>