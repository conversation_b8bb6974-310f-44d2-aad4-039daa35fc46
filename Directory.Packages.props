<Project>
  <!-- Central Package Management - Package Versions -->
  <!-- This file defines the authoritative versions for all PackageReference entries -->
  <ItemGroup>
    <!-- Build & Development Tools -->
    <PackageVersion Include="GitInfo" Version="3.5.0" />
    <PackageVersion Include="GitVersion.MsBuild" Version="6.4.0" />
    <PackageVersion Include="Nerdbank.GitVersioning" Version="3.7.115" />
    <!-- ShoppaAB Internal -->
    <PackageVersion Include="shoppaab.openid.constants" Version="0.2.4" />
    <PackageVersion Include="ShoppaAB.Services.IO" Version="2.0.32" />
    <PackageVersion Include="ShoppaAB.ThinRpc" Version="4.0.13" />
    <PackageVersion Include="ShoppaAB.ThinRpc.Async" Version="3.1.8" />
    <!-- gRPC & Protobuf -->
    <PackageVersion Include="Google.Protobuf" Version="3.31.1" />
    <PackageVersion Include="Grpc" Version="2.46.6" />
    <PackageVersion Include="Grpc.Core" Version="2.46.6" />
    <PackageVersion Include="Grpc.Tools" Version="2.72.0" />
    <PackageVersion Include="protobuf-net" Version="2.0.0.668" />
    <!-- Microsoft Extensions -->
    <PackageVersion Include="Microsoft.ApplicationInsights" Version="2.23.0" />
    <PackageVersion Include="Microsoft.ApplicationInsights.TraceListener" Version="2.21.0" />
    <PackageVersion Include="Microsoft.ApplicationInsights.WorkerService" Version="2.23.0" />
    <PackageVersion Include="Microsoft.AspNet.WebApi.OwinSelfHost" Version="5.3.0" />
    <PackageVersion Include="Microsoft.AspNet.WebApi.SelfHost" Version="5.2.7" />
    <PackageVersion Include="Microsoft.Azure.KeyVault.Core" Version="1.0.0" />
    <PackageVersion Include="Microsoft.Azure.ServiceBus" Version="5.1.2" />
    <PackageVersion Include="Microsoft.Bcl.AsyncInterfaces" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Data.Edm" Version="5.8.4" />
    <PackageVersion Include="Microsoft.Data.OData" Version="5.8.4" />
    <PackageVersion Include="Microsoft.Data.Services.Client" Version="5.8.4" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Commandline" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.environmentvariables" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.FileExtensions" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.UserSecrets" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.DependencyModel" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.FileProviders.Abstractions" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.FileProviders.Physical" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.FileSystemGlobbing" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Hosting.Systemd" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Hosting.WindowsServices" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Logging.ApplicationInsights" Version="2.23.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Configuration" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Console" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Debug" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Logging.EventLog" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Logging.EventSource" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Options" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Primitives" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Owin.Hosting" Version="4.2.3" />
    <!-- Application Insights -->
    <PackageVersion Include="Microsoft.ApplicationInsights.Agent.Intercept" Version="2.4.0" />
    <PackageVersion Include="Microsoft.ApplicationInsights.DependencyCollector" Version="2.23.0" />
    <PackageVersion Include="Microsoft.ApplicationInsights.EventCounterCollector" Version="2.23.0" />
    <PackageVersion Include="Microsoft.ApplicationInsights.PerfCounterCollector" Version="2.23.0" />
    <PackageVersion Include="Microsoft.ApplicationInsights.WindowsServer" Version="2.23.0" />
    <PackageVersion Include="Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel" Version="2.23.0" />
    <!-- Database -->
    <PackageVersion Include="Dapper" Version="2.1.66" />
    <PackageVersion Include="MySql.Data" Version="8.0.20" />
    <PackageVersion Include="MySqlConnector" Version="2.4.0" />
    <!-- Logging (Serilog) -->
    <PackageVersion Include="Serilog" Version="4.3.0" />
    <PackageVersion Include="Serilog.Enrichers.Thread" Version="4.0.0" />
    <PackageVersion Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageVersion Include="Serilog.Extensions.Logging" Version="9.0.0" />
    <PackageVersion Include="Serilog.Settings.Configuration" Version="9.0.0" />
    <PackageVersion Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageVersion Include="Serilog.Sinks.Debug" Version="3.0.0" />
    <PackageVersion Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageVersion Include="Serilog.Sinks.Seq" Version="9.0.0" />
    <PackageVersion Include="SerilogTraceListener" Version="3.2.0" />
    <!-- Migration & Database -->
    <PackageVersion Include="FluentMigrator" Version="7.1.0" />
    <PackageVersion Include="FluentMigrator.Runner" Version="7.1.0" />
    <!-- Redis -->
    <PackageVersion Include="StackExchange.Redis" Version="2.5.61" />
    <PackageVersion Include="Pipelines.Sockets.Unofficial" Version="2.2.2" />
    <!-- System Extensions -->
    <PackageVersion Include="System.Buffers" Version="4.5.1" />
    <PackageVersion Include="System.Diagnostics.DiagnosticSource" Version="9.0.8" />
    <PackageVersion Include="System.Diagnostics.PerformanceCounter" Version="5.0.0" />
    <PackageVersion Include="System.IO.Compression" Version="4.3.0" />
    <PackageVersion Include="System.IO.Pipelines" Version="5.0.1" />
    <PackageVersion Include="System.Memory" Version="4.5.5" />
    <PackageVersion Include="System.Numerics.Vectors" Version="4.5.0" />
    <PackageVersion Include="System.Resources.Extensions" Version="8.0.0" />
    <PackageVersion Include="System.Runtime.CompilerServices.Unsafe" Version="6.0.0" />
    <PackageVersion Include="System.Spatial" Version="5.8.4" />
    <PackageVersion Include="System.Threading.Channels" Version="5.0.0" />
    <PackageVersion Include="System.Threading.Tasks.Extensions" Version="4.5.4" />
    <PackageVersion Include="System.Diagnostics.EventLog" Version="5.0.0" />
    <PackageVersion Include="System.IO" Version="4.3.0" />
    <PackageVersion Include="System.Runtime" Version="4.3.0" />
    <PackageVersion Include="System.Runtime.InteropServices.RuntimeInformation" Version="4.3.0" />
    <PackageVersion Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />
    <PackageVersion Include="System.Security.Cryptography.Encoding" Version="4.3.0" />
    <PackageVersion Include="System.Security.Cryptography.Primitives" Version="4.3.0" />
    <PackageVersion Include="System.Security.Principal.Windows" Version="5.0.0" />
    <PackageVersion Include="System.ServiceProcess.ServiceController" Version="5.0.0" />
    <PackageVersion Include="System.Text.Encodings.Web" Version="9.0.8" />
    <PackageVersion Include="System.Text.Json" Version="9.0.8" />
    <PackageVersion Include="System.IO.Hashing" Version="6.0.0" />
    <PackageVersion Include="System.Memory.Data" Version="6.0.0" />
    <PackageVersion Include="System.Net.Http" Version="4.3.4" />
    <PackageVersion Include="System.Security.Cryptography.X509Certificates" Version="4.3.0" />
    <!-- Web API and MVC -->
    <PackageVersion Include="Antlr" Version="*******" />
    <PackageVersion Include="Microsoft.AspNet.Mvc" Version="5.2.8" />
    <PackageVersion Include="Microsoft.AspNet.Razor" Version="3.2.8" />
    <PackageVersion Include="Microsoft.AspNet.Web.Optimization" Version="1.1.3" />
    <PackageVersion Include="Microsoft.AspNet.WebApi.Client" Version="5.2.8" />
    <PackageVersion Include="Microsoft.AspNet.WebApi.Core" Version="5.2.8" />
    <PackageVersion Include="Microsoft.AspNet.WebPages" Version="3.2.8" />
    <PackageVersion Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" Version="3.6.0" />
    <PackageVersion Include="Microsoft.Web.Infrastructure" Version="2.0.0" />
    <PackageVersion Include="Modernizr" Version="2.8.3" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="System.ValueTuple" Version="4.5.0" />
    <PackageVersion Include="WebGrease" Version="1.6.0" />
    <!-- Testing -->
    <PackageVersion Include="coverlet.collector" Version="3.1.2" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.3.2" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="MSTest.TestAdapter" Version="2.2.10" />
    <PackageVersion Include="MSTest.TestFramework" Version="2.2.10" />
    <PackageVersion Include="NUnit" Version="3.9.0" />
    <PackageVersion Include="NUnit3TestAdapter" Version="3.9.0" />
    <PackageVersion Include="NUnitLite" Version="3.6.1" />
    <PackageVersion Include="xunit" Version="2.9.3" />
    <PackageVersion Include="xunit.abstractions" Version="2.0.3" />
    <PackageVersion Include="xunit.analyzers" Version="1.18.0" />
    <PackageVersion Include="xunit.assert" Version="2.9.3" />
    <PackageVersion Include="xunit.core" Version="2.9.3" />
    <PackageVersion Include="xunit.extensibility.core" Version="2.9.3" />
    <PackageVersion Include="xunit.extensibility.execution" Version="2.9.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.4.1" />
    <!-- Utilities -->
    <PackageVersion Include="CommandLineParser" Version="2.9.1" />
    <PackageVersion Include="CsvHelper" Version="15.0.5" />
    <!-- Email & Security -->
    <PackageVersion Include="BouncyCastle" Version="1.8.5" />
    <PackageVersion Include="MailKit" Version="2.5.0" />
    <PackageVersion Include="MimeKit" Version="2.5.0" />
    <!-- AWS -->
    <PackageVersion Include="AWSSDK.S3" Version="3.7.205.9" />
    <!-- Azure Storage -->
    <PackageVersion Include="Azure.Core" Version="1.44.1" />
    <PackageVersion Include="Azure.Storage.Blobs" Version="12.24.1" />
    <PackageVersion Include="Azure.Storage.Common" Version="12.23.0" />
    <PackageVersion Include="WindowsAzure.Storage" Version="9.3.3" />
    <!-- Microsoft Identity -->
    <PackageVersion Include="Microsoft.Identity.Client" Version="4.49.1" />
    <PackageVersion Include="Microsoft.IdentityModel.Abstractions" Version="6.27.0" />
    <PackageVersion Include="Microsoft.IdentityModel.Protocols" Version="6.27.0" />
    <PackageVersion Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="6.27.0" />
    <PackageVersion Include="Microsoft.IdentityModel.Tokens" Version="6.27.0" />
    <PackageVersion Include="Microsoft.Owin.Security" Version="4.2.2" />
    <!-- Shoppa-specific packages -->
    <PackageVersion Include="Mediablob.Remoting" Version="3.4.1727" />
    <PackageVersion Include="Shoppa.Remoting" Version="3.4.25" />
    <PackageVersion Include="ShoppaAB.Extensions.Dapper" Version="1.1.0" />
    <PackageVersion Include="ShoppaAB.Remoting" Version="3.1.67" />
    <!-- Graphics & UI -->
    <PackageVersion Include="FreeImage.NET" Version="3.18.1" />
    <PackageVersion Include="SkiaSharp" Version="2.88.3" />
    <PackageVersion Include="SkiaSharp.NativeAssets.Linux" Version="2.88.3" />
    <PackageVersion Include="SkiaSharp.NativeAssets.Win32" Version="2.88.3" />
    <!-- System -->
    <PackageVersion Include="System.CommandLine" Version="2.0.0-beta5.25306.1" />
    <PackageVersion Include="System.Configuration.ConfigurationManager" Version="8.0.0" />
    <PackageVersion Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageVersion Include="system.data.sqlite" Version="1.0.118" />
    <PackageVersion Include="System.Drawing.Common" Version="9.0.6" />
    <!-- Team Foundation Server -->
    <PackageVersion Include="Microsoft.TeamFoundationServer.ExtendedClient" Version="16.170.0" />
    <!-- Additional packages from legacy migration -->
    <PackageVersion Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageVersion Include="ThisAssembly.Constants" Version="2.0.6" />
    <PackageVersion Include="ShoppaAB.ThinRpc" Version="4.0.13" />
    <PackageVersion Include="System.Buffers" Version="4.5.1" />
    <PackageVersion Include="System.Memory" Version="4.5.4" />
    <PackageVersion Include="System.Numerics.Vectors" Version="4.5.0" />
    <PackageVersion Include="System.Runtime.CompilerServices.Unsafe" Version="5.0.0" />
  </ItemGroup>
</Project>