<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net471</TargetFramework>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A285B77C-E09E-4913-B54A-6A4EBF280FAC}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Shoppa.WinApi</RootNamespace>
    <AssemblyName>Shoppa.WinApi</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="GitInfo" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\..\AssemblyInfoInc.cs">
      <Link>Properties\AssemblyInfoInc.cs</Link>
    </Compile>
    <Compile Include="DllImport\AdvApi32.cs" />
    <Compile Include="DllImport\AdvApi32.Enums\AdvApi32.LOGON32.cs" />
    <Compile Include="DllImport\CfgMgr32.cs" />
    <Compile Include="DllImport\ComCtl32.cs" />
    <Compile Include="DllImport\ComCtl32.Enums\ComCtl32.MCN.cs" />
    <Compile Include="DllImport\ComCtl32.Enums\ComCtl32.TRACKMOUSEEVENT.cs" />
    <Compile Include="DllImport\Gdi32.cs" />
    <Compile Include="DllImport\Gdi32.Enums\Gdi32.DeviceCap.cs" />
    <Compile Include="DllImport\Gdi32.Enums\Gdi32.DrawingMode.cs" />
    <Compile Include="DllImport\Gdi32.Enums\Gdi32.LOGBRUSH.cs" />
    <Compile Include="DllImport\Gdi32.Enums\Gdi32.PenStyles.cs" />
    <Compile Include="DllImport\Gdi32.Enums\Gdi32.TernaryRasterOperations.cs" />
    <Compile Include="DllImport\Kernel32.Enums\Kernel32.CreationDisposition.cs" />
    <Compile Include="DllImport\Kernel32.cs" />
    <Compile Include="DllImport\Kernel32.Enums\Kernel32.DesiredAccess.cs" />
    <Compile Include="DllImport\Kernel32.Enums\Kernel32.ShareMode.cs" />
    <Compile Include="DllImport\SetupApi.cs" />
    <Compile Include="DllImport\SetupApi.Enums\SetupApi.DIGCF.cs" />
    <Compile Include="DllImport\SetupApi.Enums\SetupApi.SP_DEVICE_INTERFACE_DATA.cs" />
    <Compile Include="DllImport\SetupApi.Enums\SetupApi.SP_DEVICE_INTERFACE_DETAIL_DATA.cs" />
    <Compile Include="DllImport\SetupApi.Enums\SetupApi.SP_DEVINFO_DATA.cs" />
    <Compile Include="DllImport\Shell32.cs" />
    <Compile Include="DllImport\Shell32.Enums\Shell32.APPBARDATA.cs" />
    <Compile Include="DllImport\User32.cs" />
    <Compile Include="DllImport\User32.Enums\User32.ARGB.cs" />
    <Compile Include="DllImport\User32.Enums\User32.BLENDFUNCTION.cs" />
    <Compile Include="DllImport\User32.Enums\User32.DCX.cs" />
    <Compile Include="DllImport\User32.Enums\User32.HookType.cs" />
    <Compile Include="DllImport\User32.Enums\User32.IconInfo.cs" />
    <Compile Include="DllImport\User32.Enums\User32.MB_RESULT.cs" />
    <Compile Include="DllImport\User32.Enums\User32.MOUSEEVENTF.cs" />
    <Compile Include="DllImport\User32.Enums\User32.NCCALCSIZE_PARAMS.cs" />
    <Compile Include="DllImport\User32.Enums\User32.NCHITTEST.cs" />
    <Compile Include="DllImport\User32.Enums\User32.Point.cs" />
    <Compile Include="DllImport\User32.Enums\User32.RedrawWindowFlags.cs" />
    <Compile Include="DllImport\User32.Enums\User32.SetWindowPosFlags.cs" />
    <Compile Include="DllImport\User32.Enums\User32.ShowWindowCommand.cs" />
    <Compile Include="DllImport\User32.Enums\User32.Size.cs" />
    <Compile Include="DllImport\User32.Enums\User32.SysCommand.cs" />
    <Compile Include="DllImport\User32.Enums\User32.WindowLongFlags.cs" />
    <Compile Include="DllImport\User32.Enums\User32.WindowMessage.cs" />
    <Compile Include="DllImport\User32.Enums\User32.WINDOWPOS.cs" />
    <Compile Include="DllImport\User32.Enums\User32.WindowStyles.cs" />
    <Compile Include="DllImport\User32.Enums\User32.WindowStylesEx.cs" />
    <Compile Include="DllImport\User32.Enums\User32.RECT.cs" />
    <Compile Include="DllImport\UxTheme.cs" />
    <Compile Include="DllImport\WinSpool.Enums\WinSpool.DEVMODE.cs" />
    <Compile Include="DllImport\WinSpool.Enums\WinSpool.DM.cs" />
    <Compile Include="DllImport\WinSpool.Enums\WinSpool.DMFIELDS.cs" />
    <Compile Include="DllImport\WinSpool.Enums\WinSpool.DMPAPER.cs" />
    <Compile Include="DllImport\WinSpool.Enums\WinSpool.DRIVER_INFO.cs" />
    <Compile Include="DllImport\WinSpool.Enums\WinSpool.PRINTER_DEFAULTS.cs" />
    <Compile Include="DllImport\WinSpool.Enums\WinSpool.PRINTER_INFO.cs" />
    <Compile Include="Drawing\Drawing.cs" />
    <Compile Include="Drawing\Drawing.RGB.cs" />
    <Compile Include="Drawing\SafeGdiObject.cs" />
    <Compile Include="Printing\Printing.cs" />
    <Compile Include="Printing\PrintDriverInfo.cs" />
    <Compile Include="Shared\Lib.cs" />
    <Compile Include="Shared\SafeCoTaskMemHandle.cs" />
    <Compile Include="Printing\SafeHPRINTER.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="DllImport\WinSpool.cs" />
    <Compile Include="DllImport\ComCtl32.Enums\ComCtl32.DTM.cs" />
    <Compile Include="DllImport\ComCtl32.Enums\ComCtl32.NMDAYSTATE.cs" />
    <Compile Include="DllImport\ComCtl32.Enums\ComCtl32.NMHDR.cs" />
    <Compile Include="Shared\WinBase\SYSTEMTIME.cs" />
    <Compile Include="Shared\WinError\Win32Error.ErrorCodes.cs" />
    <Compile Include="Shared\WinError\Win32Error.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\..\..\packages\GitInfo.2.0.26\build\GitInfo.targets" Condition="Exists('..\packages\GitInfo.2.0.26\build\GitInfo.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\..\packages\GitInfo.2.0.26\build\GitInfo.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\GitInfo.2.0.26\build\GitInfo.targets'))" />
    <Error Condition="!Exists('..\..\..\packages\GitInfo.2.0.26\build\GitInfo.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\..\packages\GitInfo.2.0.26\build\GitInfo.targets'))" />
  </Target>
  <Import Project="..\..\..\packages\GitInfo.2.0.26\build\GitInfo.targets" Condition="Exists('..\..\..\packages\GitInfo.2.0.26\build\GitInfo.targets')" />
</Project>