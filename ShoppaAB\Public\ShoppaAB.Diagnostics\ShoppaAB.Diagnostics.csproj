﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net471</TargetFramework>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{5F542A17-7882-4475-B1F3-4BDFCF973703}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ShoppaAB.Diagnostics</RootNamespace>
    <AssemblyName>ShoppaAB.Diagnostics</AssemblyName>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>CS1591,CS1573</NoWarn>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="GitInfo" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Options" />
    <PackageReference Include="Microsoft.Extensions.Primitives" />
    <PackageReference Include="ShoppaAB.ThinRpc" />
    <PackageReference Include="System.Buffers" />
    <PackageReference Include="System.Memory" />
    <PackageReference Include="System.Numerics.Vectors" />
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\..\AssemblyInfoInc.cs">
      <Link>Properties\AssemblyInfoInc.cs</Link>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ShoppaAB.Remoting.User\ShoppaAB.Remoting.User.csproj">
      <Project>{BDA95DAB-F60A-4EE6-BC71-D7123D265960}</Project>
      <Name>ShoppaAB.Remoting.User</Name>
    </ProjectReference>
  </ItemGroup>

  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
</Project>