﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net471</TargetFramework>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{8C32DF8E-644F-4FA0-BA08-F897E90F14AE}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Mediablob.Objects</RootNamespace>
    <AssemblyName>Mediablob.Objects</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="GitInfo" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="..\..\AssemblyInfoInc.cs">
      <Link>Properties\AssemblyInfoInc.cs</Link>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Public\Mediablob.Objects.Common\Mediablob.Objects.Common.csproj">
      <Project>{217A63A6-EB34-4A78-823F-A54B2BFF004D}</Project>
      <Name>Mediablob.Objects.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Public\Mediablob.Objects.Public\Mediablob.Objects.Public.csproj">
      <Project>{9F7F8D75-B8E7-4EA4-B6E3-8B5747A0C93E}</Project>
      <Name>Mediablob.Objects.Public</Name>
    </ProjectReference>
  </ItemGroup>
</Project>